<?xml version="1.0" encoding="UTF-8"?>

<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
  <suppress until="2025-12-31Z">
    <notes>
      easy-rules-mvel is not included in our dependencies, and if it was then we wouldn't parse untrusted content with
      it. If a maintenance update that resolves this is released, then we could update and remove this suppression.
      https://web.nvd.nist.gov/view/vuln/detail?vulnId=CVE-2023-50571
      https://github.com/j-easy/easy-rules/issues/419
    </notes>
    <cve>CVE-2023-50571</cve>
  </suppress>
  <suppress until="2025-12-31Z">
    <notes>
      Suppress false-positive due to MongoDB driver being identified as MongoDB. This CVE is old and refers to running
      MongoDB on a Red Hat Satellite 6 system, therefore is completely irrelevant to Xplain
      An issue is already logged on the DependencyCheck project about the mongodb ecosystem:
      https://github.com/jeremylong/DependencyCheck/issues/6358.
    </notes>
    <cve>CVE-2014-8180</cve>
  </suppress>
  <suppress until="2025-12-31Z">
    <notes>
      Suppress false positive reported on Hazelcast due to a vulnerability in the Management Center component which
      is not part of Xplain.
    </notes>
    <cve>CVE-2024-56518</cve>
  </suppress>

</suppressions>
