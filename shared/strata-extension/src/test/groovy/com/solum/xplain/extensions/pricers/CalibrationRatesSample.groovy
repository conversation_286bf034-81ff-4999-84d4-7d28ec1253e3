package com.solum.xplain.extensions.pricers

import static com.opengamma.strata.basics.currency.Currency.BRL
import static com.opengamma.strata.basics.currency.Currency.CHF
import static com.opengamma.strata.basics.currency.Currency.CLP
import static com.opengamma.strata.basics.currency.Currency.GBP
import static com.opengamma.strata.basics.currency.Currency.USD

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.index.OvernightIndex
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.collect.array.DoubleMatrix
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries
import com.opengamma.strata.data.MarketDataFxRateProvider
import com.opengamma.strata.market.curve.Curve
import com.opengamma.strata.market.curve.CurveInfoType
import com.opengamma.strata.market.curve.CurveMetadata
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.CurveParameterSize
import com.opengamma.strata.market.curve.Curves
import com.opengamma.strata.market.curve.InterpolatedNodalCurve
import com.opengamma.strata.market.curve.JacobianCalibrationMatrix
import com.opengamma.strata.market.curve.NodalCurve
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.solum.xplain.extensions.constants.OvernightIndexConstants
import java.time.LocalDate

class CalibrationRatesSample {
  // spotless:off
  private static final double[][] GBP_SONIA_JACOBIAN_MATRIX = [
      [0.9999805154481485, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.9994548726683539, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.9988298366398755, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.9983179300170382, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.9977988284474907, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.9972714652710186, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9968117045903453, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1102230246251565E-16, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9963599286639047, 0.0, 0.0, 0.0, 0.0, -3.148944155648185E-18, 0.0, 6.844240336761216E-16, 4.947490307632722E-16, 3.0410594891527794E-17, 7.52448029989699E-17, -7.26018385875449E-17, 1.7719173160293325E-16, 2.272250917841257E-16, -2.5697429789906787E-17, 4.3262686104732666E-16, -2.2147335577252373E-16, -8.84066947303348E-18, 1.617645969409813E-16, 1.7322256389056697E-16, -2.7595968747427074E-17, -7.269525152536752E-17, 9.529536494556045E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9959157087503717, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9954879033491978, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9950849618534859, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9946844065314959, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.097101153850024E-19, 0.0, 0.0, 0.0, 0.0, 0.9942828734775053, 0.0, 1.8535020339470958E-16, -9.435738818128029E-17, -2.0858962790238392E-16, -2.5307137442686626E-17, 7.227440870532689E-17, 1.4536333776543774E-16, -4.401804145494491E-17, -1.4367820389643046E-16, -3.520479370257548E-16, 7.835626033712823E-17, 3.130906453140822E-17, 2.0298841750127052E-16, 2.504491053855693E-16, 2.4532858773298636E-16, 9.456784042915362E-17, 3.3006375207305684E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.794811343953435E-4, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9962518946499396, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.131050238350416E-18, 0.0, 0.0, 0.0, 0.0, -0.0025737256187044442, 0.0, 0.9971385094196744, -9.669172961002038E-17, -2.796485385381366E-16, -4.040177579398103E-16, -4.874052817197103E-16, -3.786856234867532E-16, -4.0160687715294854E-16, -1.064274336873821E-16, 1.9750488843154382E-16, -4.0744355775031495E-16, -1.909871873475255E-16, -1.8297086422598835E-16, -2.5566961924804254E-16, -1.0979640755982227E-16, 1.799521496441761E-17, -4.831602986080617E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0384668648997079E-16, 0.0, 0.0, 0.0, 0.0, -0.0016625937152792616, 0.0, -0.0033366383549731805, 0.9996490086592251, -1.027104317406188E-16, 1.5070459414794959E-18, 2.5434012638427663E-16, 2.657850383883982E-16, 1.3815166719926589E-17, -1.4250282147577626E-16, -4.2026466327103324E-16, -1.2096167244290895E-16, 5.973521189498642E-17, -1.6034198033903447E-17, -1.2772998102499243E-16, -1.0993132402645212E-16, -3.91380043608322E-17, 3.4829486571328477E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.847824484159E-17, 0.0, 0.0, 0.0, 0.0, -0.0012433289891409566, 0.0, -0.0024952212647577233, -0.0037808430526737923, 1.0022484597437207, -1.7538736832305325E-16, -2.7855132385818805E-16, 6.127829799235727E-17, 2.1776026037223562E-17, -3.421329522781208E-16, 1.3117318670598856E-16, 1.711931283475878E-16, 2.2544611718763717E-16, 2.2377659539626424E-16, 1.0173422885384763E-16, -2.0625146386523806E-16, 9.86014113239786E-17, -1.0433074217920668E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.458483721311194E-18, 0.0, 0.0, 0.0, 0.0, -0.0010008773140100745, 0.0, -0.002008648056261368, -0.0030435709874889277, -0.004031033695910093, 1.0049006629974544, 8.225240096344181E-17, 4.1041175734269677E-16, -9.537016364391397E-17, 3.835007822463334E-17, 2.4355278481342895E-17, -1.044197211705184E-16, -5.761691153407897E-17, 3.348086321202284E-17, 1.0931886626602805E-17, 1.399967584892435E-16, 1.314154677006102E-16, 1.6595069502606252E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.469640866955871E-17, 0.0, 0.0, 0.0, 0.0, -8.443267710661465E-4, 0.0, -0.001694468746380886, -0.0025675159466665835, -0.003400526335131917, -0.004272512906005791, 1.0077151735632444, 1.8570948056313911E-16, -5.805201949642498E-17, -2.5790617996676547E-17, -1.284350354511372E-16, -3.0421379875668064E-16, 2.167361104782457E-17, -3.7397073721538796E-17, 8.980762114470285E-17, -1.9121292328233E-16, 5.213318383760768E-17, -5.841816830623459E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.872998148538517E-17, 0.0, 0.0, 0.0, 0.0, -7.413262634653774E-4, 0.0, -0.001487758326941703, -0.0022543013775658707, -0.0029856917584831686, -0.00375130356724047, -0.004512391794577011, 1.0109112314947175, 1.983015511459749E-16, -5.351070715043663E-17, 1.472518732015721E-16, 2.574139280929576E-16, -7.837093927748344E-17, 1.8495133148142609E-16, 2.933395798536626E-16, 1.4852648614721272E-16, 1.4237946216986985E-16, 1.6473096758201633E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.018959392221874E-17, 0.0, 0.0, 0.0, 0.0, -6.647802300782181E-4, 0.0, -0.0013341390581008035, -0.002021532302711998, -0.0026774025850223385, -0.003363960743635549, -0.004046462405607503, -0.004732081695236031, 1.0142981248517635, 4.882380572268303E-16, 2.457818297691062E-19, -2.5566457344630597E-16, -5.3497416425944433E-17, 1.1514094722898049E-16, -7.851730723251347E-19, 1.5700814468542525E-17, 6.940249156622835E-17, -4.274467065024101E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.364411533313783E-16, 0.0, 0.0, 0.0, 0.0, -6.096625330551543E-4, 0.0, -0.0012235240472082617, -0.0018539247236327918, -0.002455416042984862, -0.003085050871355355, -0.00371096553190924, -0.004339739334007863, -0.005017481380720322, 1.0181625866571153, 4.326141037890289E-17, 5.321192661805033E-17, 4.0470971507247923E-16, 1.5526934486338978E-17, 1.8366387448882405E-16, 7.407144305061246E-17, 6.19858710800697E-17, 1.7096513007380798E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.058145519854269E-17, 0.0, 0.0, 0.0, 0.0, -5.656702069426746E-4, 0.0, -0.0011352364028589525, -0.0017201483201168134, -0.0022782369357761604, -0.0028624382674177417, -0.0034431878729234166, -0.004026590308646656, -0.004655427514528768, -0.005191000275890714, 1.0221910068194286, 4.16939861670484E-16, 1.6970684277832702E-16, -3.993442708948806E-17, 1.439730487810104E-16, 1.3219982020976767E-16, 1.3614868780986722E-16, 1.916327339868129E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.485714650482148E-17, 0.0, 0.0, 0.0, 0.0, -4.987517183257026E-4, 0.0, -0.0010009385321748598, -0.001516655676582694, -0.0020087227018950387, -0.0025238133225929404, -0.0030358605545462284, -0.0035502467882930436, -0.004104692882734017, -0.00457690766749605, -0.007914255401411104, 1.0280240825156612, 5.498138770928743E-16, 1.052935010028796E-16, 4.270030729962857E-16, 2.073003024121757E-16, 1.1809672163798357E-16, 2.4223787038757383E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.4299622559084766E-17, 0.0, 0.0, 0.0, 0.0, -4.264876193984609E-4, 0.0, -8.559126235880796E-4, -0.0012969075497612683, -0.0017176790208133905, -0.0021581382002493605, -0.002595994947306254, -0.0030358517983633732, -0.0035099642399135855, -0.003913759860054412, -0.006767559532004199, -0.01357089489718215, 1.0385443733751583, -2.3887313788372453E-16, -2.569537972966964E-17, 1.4944710508175973E-16, 2.0897996874658098E-16, -8.857931749206571E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7982504952618917E-16, 0.0, 0.0, 0.0, 0.0, -3.354859836441296E-4, 0.0, -6.732825887026759E-4, -0.001020180387982604, -0.0013511699042894772, -0.0016976462715908658, -0.0020420754995462195, -0.002388078060060601, -0.002761026937301347, -0.0030786628185151452, -0.005323534056322364, -0.01067521029083934, -0.021913273152990204, 1.052733823072864, -2.447035832970311E-16, -2.3943927352984562E-17, -4.886499191392168E-16, -1.8431436932253575E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.669919266815117E-17, 0.0, 0.0, 0.0, 0.0, -2.6970887153803864E-4, 0.0, -5.412753321392793E-4, -8.201585598867769E-4, -0.0010862525646622536, -0.0013647969885586418, -0.0016416956458063217, -0.001919859159917726, -0.0022196857569314597, -0.002475044309176913, -0.00427977451495191, -0.00858217350750804, -0.017616843808477847, -0.028522321446334098, 1.069901000050908, -3.7669249230293245E-16, -2.647621705209602E-16, -7.762887554996212E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.279930727711544E-17, 0.0, 0.0, 0.0, 0.0, -2.2114477612108563E-4, 0.0, -4.438126616424224E-4, -6.724798486449243E-4, -8.906606551483291E-4, -0.0011190500437179168, -0.0013460900043098117, -0.0015741670701565976, -0.0018200065388167504, -0.0020293849309510055, -0.0035091533013399847, -0.007036857290338422, -0.014444734271334964, -0.023386558828182823, -0.029677200307055063, 1.0846839878917112, 4.666406150377611E-16, 8.291978215169138E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0444538157471748E-17, 0.0, 0.0, 0.0, 0.0, -1.5537747737737268E-4, 0.0, -3.118250993924842E-4, -4.724878620341043E-4, -6.257828388421748E-4, -7.862504188506193E-4, -9.457698836980394E-4, -0.0011060180241265073, -0.0012787461217563046, -0.00142585647614846, -0.002465549479648794, -0.004944132769558054, -0.010148945916029084, -0.016431518659383283, -0.02085135629342625, -0.04064550892499566, 1.0928652666728227, 4.440892098500626E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.982292804965894E-17, 0.0, 0.0, 0.0, 0.0, -1.147025958580719E-4, 0.0, -2.3019519275149688E-4, -3.4879948626854714E-4, -4.6196474077486667E-4, -5.804249467765807E-4, -6.981852362131795E-4, -8.164834477617608E-4, -9.439946000228916E-4, -0.0010525942491565097, -0.001820115310833927, -0.003649852427195752, -0.007492144044588545, -0.012130058203680755, -0.01539286603435833, -0.030005284307476076, -0.051987466236570246, 1.110355507440781]
  ]

  private static final double[][] BRL_CDI_JACOBIAN_MATRIX = [
      [0.9999805154481485, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.9994548726683539, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.9988298366398755, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.9983179300170382, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.9977988284474907, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.9972714652710186, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9968117045903453, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1102230246251565E-16, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9963599286639047, 0.0, 0.0, 0.0, 0.0, -3.148944155648185E-18, 0.0, 6.844240336761216E-16, 4.947490307632722E-16, 3.0410594891527794E-17, 7.52448029989699E-17, -7.26018385875449E-17, 1.7719173160293325E-16, 2.272250917841257E-16, -2.5697429789906787E-17, 4.3262686104732666E-16, -2.2147335577252373E-16, -8.84066947303348E-18, 1.617645969409813E-16, 1.7322256389056697E-16, -2.7595968747427074E-17, -7.269525152536752E-17, 9.529536494556045E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9959157087503717, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9954879033491978, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9950849618534859, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9946844065314959, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.097101153850024E-19, 0.0, 0.0, 0.0, 0.0, 0.9942828734775053, 0.0, 1.8535020339470958E-16, -9.435738818128029E-17, -2.0858962790238392E-16, -2.5307137442686626E-17, 7.227440870532689E-17, 1.4536333776543774E-16, -4.401804145494491E-17, -1.4367820389643046E-16, -3.520479370257548E-16, 7.835626033712823E-17, 3.130906453140822E-17, 2.0298841750127052E-16, 2.504491053855693E-16, 2.4532858773298636E-16, 9.456784042915362E-17, 3.3006375207305684E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.794811343953435E-4, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9962518946499396, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.131050238350416E-18, 0.0, 0.0, 0.0, 0.0, -0.0025737256187044442, 0.0, 0.9971385094196744, -9.669172961002038E-17, -2.796485385381366E-16, -4.040177579398103E-16, -4.874052817197103E-16, -3.786856234867532E-16, -4.0160687715294854E-16, -1.064274336873821E-16, 1.9750488843154382E-16, -4.0744355775031495E-16, -1.909871873475255E-16, -1.8297086422598835E-16, -2.5566961924804254E-16, -1.0979640755982227E-16, 1.799521496441761E-17, -4.831602986080617E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0384668648997079E-16, 0.0, 0.0, 0.0, 0.0, -0.0016625937152792616, 0.0, -0.0033366383549731805, 0.9996490086592251, -1.027104317406188E-16, 1.5070459414794959E-18, 2.5434012638427663E-16, 2.657850383883982E-16, 1.3815166719926589E-17, -1.4250282147577626E-16, -4.2026466327103324E-16, -1.2096167244290895E-16, 5.973521189498642E-17, -1.6034198033903447E-17, -1.2772998102499243E-16, -1.0993132402645212E-16, -3.91380043608322E-17, 3.4829486571328477E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.847824484159E-17, 0.0, 0.0, 0.0, 0.0, -0.0012433289891409566, 0.0, -0.0024952212647577233, -0.0037808430526737923, 1.0022484597437207, -1.7538736832305325E-16, -2.7855132385818805E-16, 6.127829799235727E-17, 2.1776026037223562E-17, -3.421329522781208E-16, 1.3117318670598856E-16, 1.711931283475878E-16, 2.2544611718763717E-16, 2.2377659539626424E-16, 1.0173422885384763E-16, -2.0625146386523806E-16, 9.86014113239786E-17, -1.0433074217920668E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.458483721311194E-18, 0.0, 0.0, 0.0, 0.0, -0.0010008773140100745, 0.0, -0.002008648056261368, -0.0030435709874889277, -0.004031033695910093, 1.0049006629974544, 8.225240096344181E-17, 4.1041175734269677E-16, -9.537016364391397E-17, 3.835007822463334E-17, 2.4355278481342895E-17, -1.044197211705184E-16, -5.761691153407897E-17, 3.348086321202284E-17, 1.0931886626602805E-17, 1.399967584892435E-16, 1.314154677006102E-16, 1.6595069502606252E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.469640866955871E-17, 0.0, 0.0, 0.0, 0.0, -8.443267710661465E-4, 0.0, -0.001694468746380886, -0.0025675159466665835, -0.003400526335131917, -0.004272512906005791, 1.0077151735632444, 1.8570948056313911E-16, -5.805201949642498E-17, -2.5790617996676547E-17, -1.284350354511372E-16, -3.0421379875668064E-16, 2.167361104782457E-17, -3.7397073721538796E-17, 8.980762114470285E-17, -1.9121292328233E-16, 5.213318383760768E-17, -5.841816830623459E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.872998148538517E-17, 0.0, 0.0, 0.0, 0.0, -7.413262634653774E-4, 0.0, -0.001487758326941703, -0.0022543013775658707, -0.0029856917584831686, -0.00375130356724047, -0.004512391794577011, 1.0109112314947175, 1.983015511459749E-16, -5.351070715043663E-17, 1.472518732015721E-16, 2.574139280929576E-16, -7.837093927748344E-17, 1.8495133148142609E-16, 2.933395798536626E-16, 1.4852648614721272E-16, 1.4237946216986985E-16, 1.6473096758201633E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.018959392221874E-17, 0.0, 0.0, 0.0, 0.0, -6.647802300782181E-4, 0.0, -0.0013341390581008035, -0.002021532302711998, -0.0026774025850223385, -0.003363960743635549, -0.004046462405607503, -0.004732081695236031, 1.0142981248517635, 4.882380572268303E-16, 2.457818297691062E-19, -2.5566457344630597E-16, -5.3497416425944433E-17, 1.1514094722898049E-16, -7.851730723251347E-19, 1.5700814468542525E-17, 6.940249156622835E-17, -4.274467065024101E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.364411533313783E-16, 0.0, 0.0, 0.0, 0.0, -6.096625330551543E-4, 0.0, -0.0012235240472082617, -0.0018539247236327918, -0.002455416042984862, -0.003085050871355355, -0.00371096553190924, -0.004339739334007863, -0.005017481380720322, 1.0181625866571153, 4.326141037890289E-17, 5.321192661805033E-17, 4.0470971507247923E-16, 1.5526934486338978E-17, 1.8366387448882405E-16, 7.407144305061246E-17, 6.19858710800697E-17, 1.7096513007380798E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.058145519854269E-17, 0.0, 0.0, 0.0, 0.0, -5.656702069426746E-4, 0.0, -0.0011352364028589525, -0.0017201483201168134, -0.0022782369357761604, -0.0028624382674177417, -0.0034431878729234166, -0.004026590308646656, -0.004655427514528768, -0.005191000275890714, 1.0221910068194286, 4.16939861670484E-16, 1.6970684277832702E-16, -3.993442708948806E-17, 1.439730487810104E-16, 1.3219982020976767E-16, 1.3614868780986722E-16, 1.916327339868129E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.485714650482148E-17, 0.0, 0.0, 0.0, 0.0, -4.987517183257026E-4, 0.0, -0.0010009385321748598, -0.001516655676582694, -0.0020087227018950387, -0.0025238133225929404, -0.0030358605545462284, -0.0035502467882930436, -0.004104692882734017, -0.00457690766749605, -0.007914255401411104, 1.0280240825156612, 5.498138770928743E-16, 1.052935010028796E-16, 4.270030729962857E-16, 2.073003024121757E-16, 1.1809672163798357E-16, 2.4223787038757383E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.4299622559084766E-17, 0.0, 0.0, 0.0, 0.0, -4.264876193984609E-4, 0.0, -8.559126235880796E-4, -0.0012969075497612683, -0.0017176790208133905, -0.0021581382002493605, -0.002595994947306254, -0.0030358517983633732, -0.0035099642399135855, -0.003913759860054412, -0.006767559532004199, -0.01357089489718215, 1.0385443733751583, -2.3887313788372453E-16, -2.569537972966964E-17, 1.4944710508175973E-16, 2.0897996874658098E-16, -8.857931749206571E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7982504952618917E-16, 0.0, 0.0, 0.0, 0.0, -3.354859836441296E-4, 0.0, -6.732825887026759E-4, -0.001020180387982604, -0.0013511699042894772, -0.0016976462715908658, -0.0020420754995462195, -0.002388078060060601, -0.002761026937301347, -0.0030786628185151452, -0.005323534056322364, -0.01067521029083934, -0.021913273152990204, 1.052733823072864, -2.447035832970311E-16, -2.3943927352984562E-17, -4.886499191392168E-16, -1.8431436932253575E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.669919266815117E-17, 0.0, 0.0, 0.0, 0.0, -2.6970887153803864E-4, 0.0, -5.412753321392793E-4, -8.201585598867769E-4, -0.0010862525646622536, -0.0013647969885586418, -0.0016416956458063217, -0.001919859159917726, -0.0022196857569314597, -0.002475044309176913, -0.00427977451495191, -0.00858217350750804, -0.017616843808477847, -0.028522321446334098, 1.069901000050908, -3.7669249230293245E-16, -2.647621705209602E-16, -7.762887554996212E-17],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.279930727711544E-17, 0.0, 0.0, 0.0, 0.0, -2.2114477612108563E-4, 0.0, -4.438126616424224E-4, -6.724798486449243E-4, -8.906606551483291E-4, -0.0011190500437179168, -0.0013460900043098117, -0.0015741670701565976, -0.0018200065388167504, -0.0020293849309510055, -0.0035091533013399847, -0.007036857290338422, -0.014444734271334964, -0.023386558828182823, -0.029677200307055063, 1.0846839878917112, 4.666406150377611E-16, 8.291978215169138E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0444538157471748E-17, 0.0, 0.0, 0.0, 0.0, -1.5537747737737268E-4, 0.0, -3.118250993924842E-4, -4.724878620341043E-4, -6.257828388421748E-4, -7.862504188506193E-4, -9.457698836980394E-4, -0.0011060180241265073, -0.0012787461217563046, -0.00142585647614846, -0.002465549479648794, -0.004944132769558054, -0.010148945916029084, -0.016431518659383283, -0.02085135629342625, -0.04064550892499566, 1.0928652666728227, 4.440892098500626E-16],
      [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.982292804965894E-17, 0.0, 0.0, 0.0, 0.0, -1.147025958580719E-4, 0.0, -2.3019519275149688E-4, -3.4879948626854714E-4, -4.6196474077486667E-4, -5.804249467765807E-4, -6.981852362131795E-4, -8.164834477617608E-4, -9.439946000228916E-4, -0.0010525942491565097, -0.001820115310833927, -0.003649852427195752, -0.007492144044588545, -0.012130058203680755, -0.01539286603435833, -0.030005284307476076, -0.051987466236570246, 1.110355507440781]
  ]
  // spotless:on

  static gbpRatesProvider(LocalDate date = MarketDataSample.VAL_DT, Currency ccy = GBP, String curveName = "GBP-SONIA", OvernightIndex index = OvernightIndices.GBP_SONIA) {
    var gbpSoniaFixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.00048)
      .put(LocalDate.parse("2021-02-09"), 0.000482)
      .put(LocalDate.parse("2021-02-10"), 0.000483)
      .build()

    ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(ccy): gbpOIS(curveName),])
      .indexCurve(index, gbpOIS(curveName))
      .timeSeries(PriceIndices.GB_RPI, timeSeriesProvider())
      .timeSeries(index, gbpSoniaFixings)
      .build()
  }

  static chfRatesProvider(LocalDate date = MarketDataSample.VAL_DT, Currency ccy = CHF, String curveName = "CHF-SARON", OvernightIndex index = OvernightIndices.CHF_SARON) {
    var chfSaronFixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.001001)
      .put(LocalDate.parse("2021-02-09"), 0.001211)
      .build()

    ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(ccy): gbpOIS(curveName),])
      .indexCurve(index, gbpOIS(curveName))
      .timeSeries(PriceIndices.GB_RPI, timeSeriesProvider())
      .timeSeries(index, chfSaronFixings)
      .build()
  }

  static clpRatesProvider(LocalDate date = MarketDataSample.VAL_DT, Currency ccy = CLP, String curveName = "CLP-TNA", OvernightIndex index = OvernightIndexConstants.CLP_TNA) {
    var clpTnaFixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2023-11-27"), 23075.2)
      .put(LocalDate.parse("2023-11-28"), 23080.97)
      .put(LocalDate.parse("2023-11-29"), 23086.74)
      .put(LocalDate.parse("2023-11-30"), 23092.51)
      .put(LocalDate.parse("2023-12-01"), 23092.28)
      .put(LocalDate.parse("2023-12-04"), 23115.6)
      .build()

    ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(ccy): clpOIS(curveName),])
      .indexCurve(index, clpOIS(curveName))
      .timeSeries(index, clpTnaFixings)
      .build()
  }

  static jointRatesProvider(LocalDate date = MarketDataSample.VAL_DT) {
    var gbpSoniaFixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.00048)
      .put(LocalDate.parse("2021-02-09"), 0.000482)
      .put(LocalDate.parse("2021-02-10"), 0.000483)
      .build()

    ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(GBP): gbpOIS("GBP-SONIA"), (USD): usdOIS("USD-SOFR")])
      .indexCurves([(OvernightIndices.GBP_SONIA): gbpOIS("GBP-SONIA"), (OvernightIndices.USD_SOFR): usdOIS("USD-SOFR")])
      .timeSeries(PriceIndices.GB_RPI, timeSeriesProvider())
      .timeSeries([(OvernightIndices.GBP_SONIA): gbpSoniaFixings, (OvernightIndices.USD_SOFR): gbpSoniaFixings])
      .build()
  }

  static brlRatesProvider(LocalDate date = MarketDataSample.VAL_DT) {
    var brlCdiFixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.00048)
      .put(LocalDate.parse("2021-02-09"), 0.000482)
      .put(LocalDate.parse("2021-02-10"), 0.000483)
      .build()

    ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(BRL): brlOIS(),])
      .indexCurve(OvernightIndices.BRL_CDI, brlOIS())
      .timeSeries(OvernightIndices.BRL_CDI, brlCdiFixings)
      .build()
  }

  static usdTermRatesProvider(LocalDate date = MarketDataSample.VAL_DT, Currency ccy = USD, String curveName = "USD-SOFR-6M", OvernightIndex index = OvernightIndexConstants.USD_SOFR_6M) {
    def fixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2025-02-10"),0.04200)
      .put(LocalDate.parse("2025-05-06"), 0.04500)
      .put(LocalDate.parse("2025-05-07"), 0.04500)
      .put(LocalDate.parse("2025-05-08"), 0.04247)
      .put(LocalDate.parse("2025-05-09"), 0.04500)
      .put(LocalDate.parse("2025-05-12"), 0.04200)
      .build()

    ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(ccy): usdTermOIS(curveName)])
      .indexCurve(index, usdTermOIS(curveName))
      .timeSeries(index, fixings)
      .build()
  }

  static Curve usdTermOIS(String curveName = "USD-SOFR-6M") {
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.5,0.6),
      DoubleArray.of(0.**********, 0.**********), // Expected rate used in your test
      BRL_CDI_JACOBIAN_MATRIX, // Use dummy Jacobian for now
      [CurveParameterSize.of(CurveName.of(curveName), 1)]
      )
  }



  static Curve gbpOIS(String curveName = "GBP-SONIA") {
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.0027397260273972603, 0.07671232876712329, 0.16712328767123288, 0.24383561643835616, 0.3287671232876712, 0.41643835616438357, 0.4958904109589041, 0.5808219178082191, 0.6657534246575343, 0.7479452054794521, 0.8301369863013699, 0.915068493150685, 1.0, 1.4958904109589042, 2.0, 3.0054794520547947, 4.002739726027397, 5.002739726027397, 6.002739726027397, 7.002739726027397, 8.01095890410959, 9.008219178082191, 10.005479452054795, 12.008219178082191, 15.01095890410959, 20.016438356164382, 25.02191780821918, 30.019178082191782, 40.02739726027397, 50.032876712328765),
      DoubleArray.of(0.0071119307124541545, 0.007108061720100539, 0.007005896976707344, 0.006904185186986854, 0.006702609644969989, 0.006561028600107863, 0.006439706802230806, 0.006278538059769573, 0.006147403221617649, 0.006046307738025543, 0.00593535374618033, 0.005824450891690162, 0.005733531847760627, 0.005357079284064805, 0.005165162093707441, 0.005015670098133967, 0.004996062255463457, 0.005026493636395212, 0.0050870904671968115, 0.005208355446444291, 0.005339904583506066, 0.005502260221939582, 0.005664971090560569, 0.005981357895837914, 0.0063712873738967606, 0.006657544822502992, 0.006681718416544124, 0.006578338926827578, 0.006210704326641415, 0.005800396468906743),
      GBP_SONIA_JACOBIAN_MATRIX,
      [CurveParameterSize.of(CurveName.of(curveName), 30)]
      )
  }

  static Curve clpOIS(String curveName = "CLP-TNA") {
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.0027397260273972603, 0.07671232876712329, 0.16712328767123288, 0.24383561643835616, 0.3287671232876712, 0.41643835616438357, 0.4958904109589041, 0.5808219178082191, 0.6657534246575343, 0.7479452054794521, 0.8301369863013699, 0.915068493150685, 1.0, 1.4958904109589042, 2.0, 3.0054794520547947, 4.002739726027397, 5.002739726027397, 6.002739726027397, 7.002739726027397, 8.01095890410959, 9.008219178082191, 10.005479452054795, 12.008219178082191, 15.01095890410959, 20.016438356164382, 25.02191780821918, 30.019178082191782, 40.02739726027397, 50.032876712328765),
      DoubleArray.of(0.0071119307124541545, 0.007108061720100539, 0.007005896976707344, 0.006904185186986854, 0.006702609644969989, 0.006561028600107863, 0.006439706802230806, 0.006278538059769573, 0.006147403221617649, 0.006046307738025543, 0.00593535374618033, 0.005824450891690162, 0.005733531847760627, 0.005357079284064805, 0.005165162093707441, 0.005015670098133967, 0.004996062255463457, 0.005026493636395212, 0.0050870904671968115, 0.005208355446444291, 0.005339904583506066, 0.005502260221939582, 0.005664971090560569, 0.005981357895837914, 0.0063712873738967606, 0.006657544822502992, 0.006681718416544124, 0.006578338926827578, 0.006210704326641415, 0.005800396468906743),
      GBP_SONIA_JACOBIAN_MATRIX,
      [CurveParameterSize.of(CurveName.of(curveName), 30)]
      )
  }

  static Curve usdOIS(String curveName = "USD-SOFR") {
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.0027397260273972603, 0.07671232876712329, 0.16712328767123288, 0.24383561643835616, 0.3287671232876712, 0.41643835616438357, 0.4958904109589041, 0.5808219178082191, 0.6657534246575343, 0.7479452054794521, 0.8301369863013699, 0.915068493150685, 1.0, 1.4958904109589042, 2.0, 3.0054794520547947, 4.002739726027397, 5.002739726027397, 6.002739726027397, 7.002739726027397, 8.01095890410959, 9.008219178082191, 10.005479452054795, 12.008219178082191, 15.01095890410959, 20.016438356164382, 25.02191780821918, 30.019178082191782, 40.02739726027397, 50.032876712328765),
      DoubleArray.of(0.0101229307124541545, 0.009428061720100539, 0.0302305896976707344, 0.005904185186986854, 0.007702609644969989, 0.006561028600107863, 0.006439706802230806, 0.006278538059769573, 0.006147403221617649, 0.006046307738025543, 0.00593535374618033, 0.005824450891690162, 0.005733531847760627, 0.005357079284064805, 0.005165162093707441, 0.005015670098133967, 0.004996062255463457, 0.005026493636395212, 0.0050870904671968115, 0.005208355446444291, 0.005339904583506066, 0.005502260221939582, 0.005664971090560569, 0.005981357895837914, 0.0063712873738967606, 0.006657544822502992, 0.006681718416544124, 0.006578338926827578, 0.006210704326641415, 0.005800396468906743),
      GBP_SONIA_JACOBIAN_MATRIX,
      [CurveParameterSize.of(CurveName.of(curveName), 30)]
      )
  }

  static Curve brlOIS(String curveName = "BRL-CDI") {
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.0027397260273972603, 0.07671232876712329, 0.16712328767123288, 0.24383561643835616, 0.3287671232876712, 0.41643835616438357, 0.4958904109589041, 0.5808219178082191, 0.6657534246575343, 0.7479452054794521, 0.8301369863013699, 0.915068493150685, 1.0, 1.4958904109589042, 2.0, 3.0054794520547947, 4.002739726027397, 5.002739726027397, 6.002739726027397, 7.002739726027397, 8.01095890410959, 9.008219178082191, 10.005479452054795, 12.008219178082191, 15.01095890410959, 20.016438356164382, 25.02191780821918, 30.019178082191782, 40.02739726027397, 50.032876712328765),
      DoubleArray.of(0.0071119307124541545, 0.007108061720100539, 0.007005896976707344, 0.006904185186986854, 0.006702609644969989, 0.006561028600107863, 0.006439706802230806, 0.006278538059769573, 0.006147403221617649, 0.006046307738025543, 0.00593535374618033, 0.005824450891690162, 0.005733531847760627, 0.005357079284064805, 0.005165162093707441, 0.005015670098133967, 0.004996062255463457, 0.005026493636395212, 0.0050870904671968115, 0.005208355446444291, 0.005339904583506066, 0.005502260221939582, 0.005664971090560569, 0.005981357895837914, 0.0063712873738967606, 0.006657544822502992, 0.006681718416544124, 0.006578338926827578, 0.006210704326641415, 0.005800396468906743),
      BRL_CDI_JACOBIAN_MATRIX,
      [CurveParameterSize.of(CurveName.of(curveName), 30)]
      )
  }

  static NodalCurve nodalCurve(
    CurveMetadata metadata,
    DoubleArray xValues,
    DoubleArray yValues,
    double[][] jacobianMatrix,
    List<CurveParameterSize> parameterSizes) {
    var jacobianMetadata = metadata
      .withInfo(CurveInfoType.JACOBIAN, JacobianCalibrationMatrix.of(parameterSizes, DoubleMatrix.copyOf(jacobianMatrix)))
    return InterpolatedNodalCurve.of(jacobianMetadata,
      xValues,
      yValues,
      CurveInterpolators.LINEAR)
  }

  static LocalDateDoubleTimeSeries timeSeriesProvider() {
    LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2017-01-01"), 100)
      .build()
  }
}
