package com.solum.xplain.xm.settings;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.settings.VersionedSettings;
import java.time.LocalDate;
import java.util.Objects;
import lombok.Data;

@Data
public class ExceptionManagementSettings extends VersionedSettings {
  private IpvValueCurrencyType currencyType;
  private IpvValueNavLevel navLevel;
  private Integer onboardingPeriod;

  public static ExceptionManagementSettings empty() {
    ExceptionManagementSettings settings = new ExceptionManagementSettings();
    settings.setValidFrom(LocalDate.ofEpochDay(0));
    settings.setState(State.ACTIVE);
    settings.setCurrencyType(IpvValueCurrencyType.TRADE_CCY);
    settings.setNavLevel(IpvValueNavLevel.TRADE_LEVEL); // We might need to change it later
    settings.setOnboardingPeriod(1); // default value is 1
    return settings;
  }

  @Override
  public boolean valueEquals(Object entity) {
    if (entity instanceof ExceptionManagementSettings item) {
      return super.valueEquals(item)
          && this.currencyType == item.currencyType
          && this.navLevel == item.navLevel
          && Objects.equals(this.onboardingPeriod, item.onboardingPeriod);
    }
    return false;
  }
}
