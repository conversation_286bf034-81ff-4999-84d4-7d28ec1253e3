package com.solum.xplain.core.classifiers.type;

import com.opengamma.strata.basics.date.BusinessDayConvention;
import com.opengamma.strata.basics.date.BusinessDayConventions;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum SupportedBusinessDayConvention {
  FOLLOWING(BusinessDayConventions.FOLLOWING),
  MODIFIED_FOLLOWING(BusinessDayConventions.MODIFIED_FOLLOWING),
  MODIFIED_FOLLOWING_BI_MONTHLY(BusinessDayConventions.MODIFIED_FOLLOWING_BI_MONTHLY),
  MODIFIED_PRECEDING(BusinessDayConventions.MODIFIED_PRECEDING),
  NEAREST(BusinessDayConventions.NEAREST),
  NO_ADJUST(BusinessDayConventions.NO_ADJUST),
  PRECEDING(BusinessDayConventions.PRECEDING);

  private final BusinessDayConvention businessDayConvention;

  public BusinessDayConvention getBusinessDayConvention() {
    return businessDayConvention;
  }

  public String label() {
    return businessDayConvention.getName();
  }

  public static Optional<SupportedBusinessDayConvention> of(String label) {
    return Stream.of(values()).filter(v -> v.label().equalsIgnoreCase(label)).findAny();
  }

  public static List<String> supportedBusinessDayConventions() {
    return Stream.of(values()).map(SupportedBusinessDayConvention::label).toList();
  }
}
