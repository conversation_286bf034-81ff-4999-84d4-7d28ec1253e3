package com.solum.xplain.core.viewconfig.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/** Wrapper annotation to allow multiple {@link ConfigurableViewMapping} annotations. */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ConfigurableViewMappings {
  ConfigurableViewMapping[] value();
}
