package com.solum.xplain.core.viewconfig.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * If set then specifies that this field is a subfield identifier when this class is used in a
 * collection. The classifier can be utilized to identify the possible subfield identifiers of the
 * array.
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ConfigurableViewSubfield {
  /**
   * Enables the explicit passing of a classifier for a subfield identifier on a non-enum field.
   * This classifier can be utilized to identify the possible values of the field.
   */
  String classifier() default "";
}
