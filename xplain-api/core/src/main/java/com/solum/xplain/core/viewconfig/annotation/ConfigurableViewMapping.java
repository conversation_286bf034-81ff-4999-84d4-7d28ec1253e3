package com.solum.xplain.core.viewconfig.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Defines a mapping from a field exposed by a nested object. This behaves like {@link
 * ConfigurableViewField} but is used on the parent field to map fields from a nested object. Used
 * when the same object is repeated for multiple properties and the fields inside the object should
 * be mapped to different names. All fields from the nested object will be exposed in the palette by
 * default and any fields that are not explicitly mapped will not be renamed. {@link #ignore()} can
 * be specified to exclude fields from the palette. {@code @ConfigurableViewMapping} can be repeated
 * to map/ignore multiple fields.
 *
 * <p>Example:
 *
 * <pre>
 *     public class MyView {
 *       &#064;ConfigurableViewMapping(from = "myInt", to = "intValue")
 *       private MyObject myObject;                    // will add two fields "intValue" and "myString"
 *
 *       &#064;ConfigurableViewMapping(from = "myInt", ignore = true)
 *       &#064;ConfigurableViewMapping(from = "myString", to = "stringValue")
 *       private MyObject prefixedObject;              // will add one field "stringValue"
 *     }
 *
 *     public class MyObject {
 *       private int myInt;
 *       private String myString;
 *     }
 *
 * </pre>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Repeatable(ConfigurableViewMappings.class)
public @interface ConfigurableViewMapping {
  String from();

  String to() default "";

  boolean ignore() default false;

  /** Use this to provide a description where client-side processing is required. */
  String deriveAs() default "";
}
