package com.solum.xplain.core.classifiers.conventions;

import com.solum.xplain.core.portfolio.value.CalculationType;
import lombok.Data;

@Data
public class Leg {

  private String index;
  private CalculationType calculationType;
  private String currency;
  private String dayCount;
  private String accrualFrequency;
  private String accrualMethod;
  private String accrualBusinessDayAdjustment;
  private String startDateBusinessDayAdjustment;
  private String endDateBusinessDayAdjustment;
  private String stubConvention;
  private String rollConvention;
  private String paymentFrequency;
  private Integer paymentDateOffset;
  private String compoundingMethod;
  private Integer fixingDateOffset;
  private Integer rateCutOffDays;
  private String lag;
}
