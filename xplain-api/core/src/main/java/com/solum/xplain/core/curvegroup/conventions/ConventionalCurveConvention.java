package com.solum.xplain.core.curvegroup.conventions;

import com.opengamma.strata.collect.named.Named;
import com.solum.xplain.core.classifiers.CurveNodeTypes;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public interface ConventionalCurveConvention extends CurveConvention {

  int getSortOrder();

  List<Named> getNodeConventions();

  default Set<String> getAllPermissibleNodeTypes() {
    return getNodeConventions().stream()
        .map(CurveNodeTypes::nodeTypeFromConvention)
        .flatMap(Optional::stream)
        .collect(Collectors.toSet());
  }

  default List<Named> nodeTypeConventions(String nodeType) {
    return getNodeConventions().stream()
        .filter(
            v ->
                CurveNodeTypes.nodeTypeFromConvention(v)
                    .filter(t -> StringUtils.equals(t, nodeType))
                    .isPresent())
        .toList();
  }
}
