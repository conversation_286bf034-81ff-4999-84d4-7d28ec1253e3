package com.solum.xplain.core.curvegroup.curve;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_CURVE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CURVE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_MARKET_DATA_KEY;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.CURVE_CONFIGURATION_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.Filtered;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions;
import com.solum.xplain.core.curvegroup.curve.dto.GetCalibratedCurvesRequest;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curve.value.CurveSearch;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/curve-group/{groupId}/curves")
public class CurveGroupCurveController {

  private final CurveGroupCurveService service;
  private final CurveGroupCurveExportService exportService;
  private final CurveGroupCurveImportService importService;

  public CurveGroupCurveController(
      CurveGroupCurveService service,
      CurveGroupCurveExportService exportService,
      CurveGroupCurveImportService importService) {
    this.service = service;
    this.exportService = exportService;
    this.importService = importService;
  }

  @Operation(summary = "Creates a new IR + Inflation curve or updates it")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> createCurve(
      @PathVariable("groupId") String groupId, @Valid @RequestBody CurveForm form) {
    return eitherErrorItemResponse(service.createCurve(groupId, form));
  }

  @Operation(summary = "Updates IR + Inflation curve")
  @PutMapping("/{curveId}/{version}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> updateCurve(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @Valid @RequestBody CurveUpdateForm form) {
    return eitherErrorItemResponse(service.updateCurve(groupId, curveId, version, form));
  }

  @Operation(summary = "Archives (sets status to ARCHIVED) IR + Inflation curve")
  @PutMapping("/{curveId}/{version}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> archiveCurve(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @Valid @RequestBody ArchiveEntityForm form) {
    return eitherErrorItemResponse(service.archiveCurve(groupId, curveId, version, form));
  }

  @Operation(summary = "Deletes (sets status to DELETED) IR + Inflation curve")
  @PutMapping("/{curveId}/{version}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> deleteCurve(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version) {
    return eitherErrorItemResponse(service.deleteCurve(groupId, curveId, version));
  }

  @Operation(summary = "Gets IR + Inflation curves")
  @GetMapping(params = {"stateDate", "withArchived"})
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<List<CurveView>> getCurves(
      @PathVariable("groupId") String groupId,
      @Valid @NotNull @RequestParam LocalDate stateDate,
      @RequestParam boolean withArchived) {
    return eitherErrorItemResponse(
        service.getCurves(groupId, BitemporalDate.newOf(stateDate), withArchived));
  }

  @Operation(summary = "Gets IR + Inflation curves")
  @GetMapping(
      params = {
        "stateDate",
        "withArchived",
        "discountingType",
        "calibrationStrippingType",
        "marketDataGroupId",
        "marketDataSource",
        "curveDate",
        "valuationDate"
      })
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<List<CurveView>> getCurvesByCalibrationParams(
      @PathVariable("groupId") String groupId,
      @ParameterObject @Valid GetCalibratedCurvesRequest getCalibratedCurvesRequest) {
    return eitherErrorItemResponse(
        service.getCurves(
            groupId,
            BitemporalDate.newOf(getCalibratedCurvesRequest.getStateDate()),
            getCalibratedCurvesRequest));
  }

  @Operation(summary = "Gets IR + Inflation curve")
  @GetMapping("/{curveId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<CurveView> getCurve(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(
        service.getCurve(groupId, curveId, BitemporalDate.newOf(stateDate)));
  }

  @Operation(summary = "Gets all IR + Inflation curve versions")
  @GetMapping("/{curveId}/versions")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<List<CurveView>> getCurveVersions(
      @PathVariable("groupId") String groupId, @PathVariable("curveId") String curveId) {
    return eitherErrorItemResponse(service.getCurveVersions(groupId, curveId));
  }

  @Operation(summary = "Gets IR + Inflation curve future versions dates")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<DateList> getCurveFutureVersions(
      @PathVariable("groupId") String groupId, @Valid CurveSearch search) {
    return eitherErrorItemResponse(service.getFutureVersions(groupId, search));
  }

  @Operation(summary = "Gets IR + Inflation curve nodes with calibration information")
  @GetMapping("/{curveId}/{version}/nodes")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<List<CurveNodeCalculatedView>> getCurveNodes(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @RequestParam(value = "valuationDate", required = false) LocalDate valDt,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var valuationDate =
        Optional.ofNullable(valDt)
            .or(() -> Optional.ofNullable(stateForm.getStateDate()))
            .orElse(version);
    var calibrationOptions =
        new CalibratedCurvesOptions(valuationDate, discountingType, calibrationStrippingType);
    return eitherErrorItemResponse(
        service.getCurveNodes(
            groupId, curveId, BitemporalDate.newOf(version), stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets IR + Inflation curves CSV")
  @GetMapping("/curves-csv")
  @Filtered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getCurvesCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getCurvesCsvBytes(groupId, BitemporalDate.newOf(stateDate), selectedColumns));
  }

  @Operation(summary = "Gets IR + Inflation curve nodes CSV")
  @GetMapping("/{curveId}/{version}/nodes/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getCurveNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      CurveConfigMarketStateForm stateForm) {
    var versionDate = BitemporalDate.newOf(version);
    return eitherErrorItemFileResponse(
        exportService.getCurveNodesCsvBytes(groupId, curveId, versionDate, stateForm));
  }

  @Operation(summary = "Gets all IR + Inflation curves nodes CSV")
  @GetMapping("/{version}/nodes/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getCurvesNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("version") LocalDate version,
      CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemFileResponse(
        exportService.getCurvesNodesCsvBytes(groupId, BitemporalDate.newOf(version), stateForm));
  }

  @Operation(summary = "Gets IR + Inflation curves MDK definitions CSV")
  @GetMapping("/mdk-definitions/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getMarketDataKeyDefinitionsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate stateDate,
      @RequestParam List<String> curveIds,
      @RequestParam(required = false) String configurationId) {
    var bitemporal = BitemporalDate.newOf(stateDate);
    return eitherErrorItemFileResponse(
        exportService.getCurvesMDKDefinitionsCsvBytes(
            groupId, configurationId, curveIds, bitemporal));
  }

  @Operation(summary = "Uploads IR + Inflation curves CSV file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadCurves(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadCurves(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads all IR + Inflation curves nodes CSV file")
  @PostMapping(value = "/upload-nodes", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadCurvesNodes(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadNodes(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads IR + Inflation curve nodes CSV file")
  @PostMapping(
      value = "/{curveId}/{version}/nodes/upload",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadCurveNodes(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadNodesForCurve(
            groupId, curveId, version, importOptions, file.getBytes()));
  }
}
