package com.solum.xplain.core.common.value


import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import java.time.LocalDate

@Builder(builderStrategy = ExternalStrategy, forClass = ChartPoint)
class ChartPointBuilder {

  static point(LocalDate date) {
    return new ChartPoint(date, 1, 1.0)
  }

  static point() {
    return point(LocalDate.parse("2017-01-01"))
  }
}
