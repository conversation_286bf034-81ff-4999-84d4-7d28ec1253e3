package com.solum.xplain.core.mdvalue.csv

import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import java.time.LocalDate
import spock.lang.Specification

class MarketDataValueCsvMapperTest extends Specification {

  def "should correctly map md value"() {
    setup:
    def mapper = new MarketDataValueCsvMapper("MD_NAME")

    when:
    def row = mapper.toCsvRow(new MarketDataValueFlatView(
      date: LocalDate.EPOCH,
      provider: "BBG",
      ticker: "T1",
      bidAsk: ValueBidAskType.ASK,
      value: 1
      ))
    def csv = new CsvOutputFile(mapper.header(), [row])
    def result = csv.write()

    then:
    result == """\
Market Data Group,Curve Date,Provider,Ticker,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,Value
MD_NAME,1970-01-01,BB<PERSON>,T1,Ask,1
"""
  }
}
